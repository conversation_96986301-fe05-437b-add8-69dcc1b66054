namespace Entity.Dto.VideoDto
{

    /// <summary>
    /// 用户转移查询DTO - 简化版，只查询员工绑定转移
    /// </summary>
    public class UserTransferQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 被转移用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 原员工ID
        /// </summary>
        public string? FromEmployeeId { get; set; }

        /// <summary>
        /// 新员工ID
        /// </summary>
        public string? ToEmployeeId { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        public string? OperatorId { get; set; }

        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string? OperatorName { get; set; }

        /// <summary>
        /// 转移原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 转移开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 转移结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 用户转移响应DTO - 简化版，只包含员工绑定转移信息
    /// </summary>
    public class UserTransferResponseDto
    {
        /// <summary>
        /// 转移记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 被转移用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 被转移用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 原员工ID
        /// </summary>
        public string? FromEmployeeId { get; set; }

        /// <summary>
        /// 原员工姓名
        /// </summary>
        public string? FromEmployeeName { get; set; }

        /// <summary>
        /// 新员工ID
        /// </summary>
        public string? ToEmployeeId { get; set; }

        /// <summary>
        /// 新员工姓名
        /// </summary>
        public string? ToEmployeeName { get; set; }

        /// <summary>
        /// 转移原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        public string? OperatorId { get; set; }

        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string? OperatorName { get; set; }

        /// <summary>
        /// 转移时间
        /// </summary>
        public DateTime TransferTime { get; set; }

    }



    /// <summary>
    /// 当前用户信息DTO
    /// </summary>
    public class CurrentUserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 用户名（兼容性属性）
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public int? RoleId { get; set; }

        /// <summary>
        /// 角色名称
        /// </summary>
        public string? RoleName { get; set; }

        /// <summary>
        /// 权限列表
        /// </summary>
        public List<string> Permissions { get; set; } = [];
    }
}
