using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 微信支付记录表
    /// </summary>
    [Table("wechat_payments")]
    public class WechatPayment : BaseEntity_ID
    {
        /// <summary>
        /// 关联红包记录ID
        /// </summary>
        [Comment("关联红包记录ID")]
        public int RewardId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Comment("用户ID")]
        public int UserId { get; set; }

        /// <summary>
        /// 微信OpenID
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("微信OpenID")]
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 商户订单号
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("商户订单号")]
        public string OutTradeNo { get; set; } = string.Empty;

        /// <summary>
        /// 订单号
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("订单号")]
        public string OrderNo { get; set; } = string.Empty;

        /// <summary>
        /// 货币类型
        /// </summary>
        [MaxLength(10)]
        [Comment("货币类型")]
        public string? Currency { get; set; } = "CNY";

        /// <summary>
        /// 商品主题
        /// </summary>
        [MaxLength(255)]
        [Comment("商品主题")]
        public string? Subject { get; set; }

        /// <summary>
        /// 商品描述
        /// </summary>
        [MaxLength(500)]
        [Comment("商品描述")]
        public string? Body { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        [MaxLength(50)]
        [Comment("支付类型")]
        public string? PaymentType { get; set; }

        /// <summary>
        /// 通知URL
        /// </summary>
        [MaxLength(255)]
        [Comment("通知URL")]
        public string? NotifyUrl { get; set; }

        /// <summary>
        /// 返回URL
        /// </summary>
        [MaxLength(255)]
        [Comment("返回URL")]
        public string? ReturnUrl { get; set; }

        /// <summary>
        /// 微信支付交易号
        /// </summary>
        [MaxLength(100)]
        [Comment("微信支付交易号")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// 支付金额(分)
        /// </summary>
        [Comment("支付金额(分)")]
        public int Amount { get; set; } = 0;

        /// <summary>
        /// 支付状态:0待支付,1支付成功,2支付失败
        /// </summary>
        [Comment("支付状态:0待支付,1支付成功,2支付失败")]
        public byte Status { get; set; } = 0;

        /// <summary>
        /// 支付描述
        /// </summary>
        [MaxLength(255)]
        [Comment("支付描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 微信返回信息
        /// </summary>
        [Comment("微信返回信息")]
        public string? WechatResponse { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        [Comment("支付时间")]
        public DateTime? PayTime { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        [MaxLength(255)]
        [Comment("失败原因")]
        public string? FailReason { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        [Comment("重试次数")]
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 退款单号
        /// </summary>
        [MaxLength(100)]
        [Comment("退款单号")]
        public string? RefundNo { get; set; }

        /// <summary>
        /// 退款金额(分)
        /// </summary>
        [Comment("退款金额(分)")]
        public int? RefundAmount { get; set; }

        /// <summary>
        /// 退款原因
        /// </summary>
        [MaxLength(255)]
        [Comment("退款原因")]
        public string? RefundReason { get; set; }

        /// <summary>
        /// 退款时间
        /// </summary>
        [Comment("退款时间")]
        public DateTime? RefundTime { get; set; }
    }
}
