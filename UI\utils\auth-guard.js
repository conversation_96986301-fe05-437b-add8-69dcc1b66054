/**
 * Authentication guard utility
 * Provides middleware functions for protecting pages and checking permissions
 */

import authService from './auth.js'

/**
 * Page authentication guard
 * Call this in onLoad of pages that require authentication
 * @param {Object} options - Page options from onLoad
 * @param {Array} requiredPermissions - Array of required permissions
 * @param {Array} allowedUserTypes - Array of allowed user types
 * @returns {boolean} - Returns true if user is authorized, false otherwise
 */
export function requireAuth (options = {}, requiredPermissions = [], allowedUserTypes = []) {
  // Check if user is logged in
  if (!authService.isLoggedIn()) {
    console.log('User not logged in, redirecting to login')
    authService.redirectToLogin()
    return false
  }

  // Check if session is valid
  if (!authService.isSessionValid()) {
    console.log('Session expired, redirecting to login')
    authService.logout()
    authService.redirectToLogin()
    return false
  }

  // Check user type if specified
  if (allowedUserTypes.length > 0) {
    const userType = authService.getUserType()
    if (!allowedUserTypes.includes(userType)) {
      console.log(`User type ${userType} not allowed, required: ${allowedUserTypes.join(', ')}`)
      uni.showToast({
        title: '您没有权限访问此页面',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return false
    }
  }

  // Check permissions if specified
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      authService.hasPermission(permission)
    )

    if (!hasAllPermissions) {
      console.log(`Missing required permissions: ${requiredPermissions.join(', ')}`)
      uni.showToast({
        title: '您没有权限访问此功能',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return false
    }
  }

  // Refresh session on successful access
  authService.refreshSession()
  return true
}

/**
 * Admin-only page guard (超管)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireAdmin (options = {}) {
  return requireAuth(options, ['*'], ['admin'])
}

/**
 * Manager-level page guard (超管, 管理)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireManager (options = {}) {
  return requireAuth(options, ['view_dashboard'], ['admin', 'manager'])
}

/**
 * Employee page guard (员工)
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireEmployee (options = {}) {
  return requireAuth(options, ['view_videos'], ['employee'])
}

/**
 * Any authenticated user guard
 * @param {Object} options - Page options from onLoad
 * @returns {boolean}
 */
export function requireAnyAuth (options = {}) {
  return requireAuth(options, [], [])
}

/**
 * Check if current user can access admin features
 * @returns {boolean}
 */
export function canAccessAdmin () {
  return authService.isLoggedIn() &&
    authService.hasPermission('*') &&
    authService.getUserType() === 'admin'
}

/**
 * Check if current user can access management features
 * @returns {boolean}
 */
export function canAccessManagement () {
  return authService.isLoggedIn() &&
    (authService.hasPermission('*') || authService.hasPermission('view_dashboard')) &&
    ['admin', 'manager'].includes(authService.getUserType())
}

/**
 * Check if current user is an employee
 * @returns {boolean}
 */
export function isEmployee () {
  return authService.isLoggedIn() &&
    authService.getUserType() === 'employee'
}

/**
 * Get user display info for UI (synchronous, uses cached data)
 * @returns {Object}
 */
export function getUserDisplayInfo () {
  if (!authService.isLoggedIn()) {
    return {
      name: '未登录',
      username: '',
      userType: '',
      userTypeText: '游客',
      avatar: '/static/images/avatar-placeholder.png',
      email: '',
      phone: '',
      realName: '',
      department: '',
      position: ''
    }
  }

  const loginInfo = authService.getLoginInfo()
  const typeMap = {
    'employee': '员工',
    'manager': '管理',
    'admin': '超管'
  }

  return {
    userId: loginInfo.userId || '',
    name: loginInfo.name || loginInfo.nickName || loginInfo.username || '用户',
    username: loginInfo.username || '',
    nickName: loginInfo.nickName || '',
    realName: loginInfo.realName || loginInfo.nickName || loginInfo.username || '',
    userType: loginInfo.userType || '',
    userTypeText: typeMap[loginInfo.userType] || '用户',
    userTypeCode: loginInfo.userTypeCode || '',
    avatar: loginInfo.avatar || '/static/images/avatar-placeholder.png',
    email: loginInfo.email || '',
    phone: loginInfo.phone || '',
    department: loginInfo.department || '',
    position: loginInfo.position || '',
    lastLoginTime: loginInfo.lastLoginTime || ''
  }
}

/**
 * Get complete user display info for UI (asynchronous, can refresh from server)
 * @param {boolean} forceRefresh - 是否强制从服务器刷新
 * @returns {Promise<Object>}
 */
export async function getCompleteUserDisplayInfo (forceRefresh = false) {
  return await authService.getCompleteUserInfo(forceRefresh)
}

/**
 * Navigation guard for tab bar pages
 * Redirects users to appropriate starting page based on their role
 */
export function handleTabBarNavigation () {
  if (!authService.isLoggedIn() || !authService.isSessionValid()) {
    authService.redirectToLogin()
    return
  }

  const userType = authService.getUserType()

  // Show tab bar for authenticated users
  uni.showTabBar()

  // Different user types see different tab bar items
  if (userType === 'employee') {
    // Employees primarily use video and quiz features
    // They can see limited dashboard data
  } else {
    // Managers/Admins have full access to all features
  }
}

/**
 * Logout with confirmation
 * @param {string} message - Custom confirmation message
 */
export function logoutWithConfirm (message = '确定要退出登录吗？') {
  uni.showModal({
    title: '确认退出',
    content: message,
    success: async (res) => {
      if (res.confirm) {
        try {
          const success = await authService.logout()

          if (success) {
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })

            setTimeout(() => {
              authService.redirectToLogin()
            }, 1500)
          } else {
            uni.showToast({
              title: '退出失败，请重试',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('Logout error:', error)
          uni.showToast({
            title: '退出失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// Export default object with all functions
export default {
  requireAuth,
  requireAdmin,
  requireManager,
  requireEmployee,
  requireAnyAuth,
  canAccessAdmin,
  canAccessManagement,
  isEmployee,
  getUserDisplayInfo,
  getCompleteUserDisplayInfo,
  handleTabBarNavigation,
  logoutWithConfirm
}
