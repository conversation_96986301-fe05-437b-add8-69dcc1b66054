using BLL.VideoService;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 用户管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class UserController(UserService userService) : BaseController
    {
        private readonly UserService _userService = userService;

        /// <summary>
        /// 微信登录/注册（自动注册新用户）
        /// </summary>
        /// <param name="wechatLoginDto">微信登录信息</param>
        /// <returns>登录结果</returns>
        [HttpPost("login", Name = "VideoUser_Login")]
        [AllowAnonymous]
        public async Task<Result<LoginResponseDto>> Login([FromBody] WechatLoginDto wechatLoginDto)
        => Success(await _userService.WechatLoginAsync(wechatLoginDto), "登录成功");

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("profile", Name = "VideoUser_GetProfile")]
        public async Task<Result<UserResponseDto?>> GetProfile()
        => Success(await _userService.GetUserAsync(GetCurrentUserIdAsInt()));

        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户详情</returns>
        [HttpGet("{userId}", Name = "VideoUser_GetUser")]
        public async Task<Result<UserResponseDto?>> GetUser(int userId)
        {
            var user = await _userService.GetUserAsync(userId);
            return Success(user);
        }

        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>用户列表</returns>
        [HttpGet(Name = "VideoUser_GetUserPagedList")]
        public async Task<Result<Entity.Dto.PagedResult<UserResponseDto>>> GetUserPagedList([FromQuery] UserQueryDto queryDto)
        => Success(await _userService.GetUserPagedListAsync(queryDto));

        /// <summary>
        /// 转移用户到新员工
        /// </summary>
        /// <param name="transferDto">转移DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("transfer", Name = "VideoUser_TransferUsers")]
        public async Task<Result<bool>> TransferUsers([FromBody] UserTransferDto transferDto)
        => Success(await _userService.BatchTransferUsersAsync(transferDto, GetCurrentUserInfo()), "用户转移成功");

        /// <summary>
        /// 访问推广链接
        /// </summary>
        /// <param name="accessDto">访问推广链接DTO</param>
        /// <returns>访问结果</returns>
        [HttpPost("access-promotion", Name = "VideoUser_AccessPromotion")]
        [AllowAnonymous]
        public async Task<Result<AccessPromotionResponseDto>> AccessPromotion([FromBody] AccessPromotionDto accessDto)
        => Success(await _userService.AccessPromotionLinkAsync(accessDto), "访问成功");
    }
}
