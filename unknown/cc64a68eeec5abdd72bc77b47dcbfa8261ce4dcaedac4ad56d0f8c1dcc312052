using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 微信支付创建DTO
    /// </summary>
    public class WechatPaymentCreateDto
    {
        /// <summary>
        /// 红包记录ID
        /// </summary>
        [Required(ErrorMessage = "红包记录ID不能为空")]
        public int RewardId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 微信OpenID
        /// </summary>
        [Required(ErrorMessage = "微信OpenID不能为空")]
        [MaxLength(100, ErrorMessage = "微信OpenID长度不能超过100个字符")]
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 商户订单号
        /// </summary>
        [Required(ErrorMessage = "商户订单号不能为空")]
        [MaxLength(100, ErrorMessage = "商户订单号长度不能超过100个字符")]
        public string OutTradeNo { get; set; } = string.Empty;

        /// <summary>
        /// 支付金额（分）
        /// </summary>
        [Required(ErrorMessage = "支付金额不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "支付金额必须大于0")]
        public int Amount { get; set; }

        /// <summary>
        /// 支付描述
        /// </summary>
        [MaxLength(200, ErrorMessage = "支付描述长度不能超过200个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 货币类型
        /// </summary>
        [MaxLength(10, ErrorMessage = "货币类型长度不能超过10个字符")]
        public string? Currency { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        [MaxLength(200, ErrorMessage = "商品标题长度不能超过200个字符")]
        public string? Subject { get; set; }

        /// <summary>
        /// 商品描述
        /// </summary>
        public string? Body { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        [MaxLength(50, ErrorMessage = "支付类型长度不能超过50个字符")]
        public string? PaymentType { get; set; }

        /// <summary>
        /// 通知URL
        /// </summary>
        [MaxLength(500, ErrorMessage = "通知URL长度不能超过500个字符")]
        public string? NotifyUrl { get; set; }

        /// <summary>
        /// 返回URL
        /// </summary>
        [MaxLength(500, ErrorMessage = "返回URL长度不能超过500个字符")]
        public string? ReturnUrl { get; set; }
    }

    /// <summary>
    /// 微信支付回调DTO
    /// </summary>
    public class WechatPaymentCallbackDto
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 结果代码
        /// </summary>
        public string? ResultCode { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PayTime { get; set; }

        /// <summary>
        /// 微信交易号
        /// </summary>
        [Required(ErrorMessage = "微信交易号不能为空")]
        [MaxLength(100, ErrorMessage = "微信交易号长度不能超过100个字符")]
        public string TransactionId { get; set; } = string.Empty;

        /// <summary>
        /// 商户订单号
        /// </summary>
        [Required(ErrorMessage = "商户订单号不能为空")]
        [MaxLength(100, ErrorMessage = "商户订单号长度不能超过100个字符")]
        public string OutTradeNo { get; set; } = string.Empty;

        /// <summary>
        /// 支付状态:0待支付,1支付成功,2支付失败
        /// </summary>
        [Required(ErrorMessage = "支付状态不能为空")]
        public byte Status { get; set; }

        /// <summary>
        /// 微信响应数据
        /// </summary>
        public string? WechatResponse { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        [MaxLength(500, ErrorMessage = "失败原因长度不能超过500个字符")]
        public string? FailReason { get; set; }
    }

    /// <summary>
    /// 微信支付退款DTO
    /// </summary>
    public class WechatPaymentRefundDto
    {
        /// <summary>
        /// 退款金额（分）
        /// </summary>
        [Required(ErrorMessage = "退款金额不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "退款金额必须大于0")]
        public int RefundAmount { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 退款原因
        /// </summary>
        [Required(ErrorMessage = "退款原因不能为空")]
        [MaxLength(200, ErrorMessage = "退款原因长度不能超过200个字符")]
        public string RefundReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 微信支付查询DTO
    /// </summary>
    public class WechatPaymentQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 红包记录ID
        /// </summary>
        public int? RewardId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 微信OpenID
        /// </summary>
        public string? OpenId { get; set; }

        /// <summary>
        /// 商户订单号
        /// </summary>
        public string? OutTradeNo { get; set; }

        /// <summary>
        /// 微信交易号
        /// </summary>
        public string? TransactionId { get; set; }

        /// <summary>
        /// 支付状态
        /// </summary>
        public byte? Status { get; set; }

        /// <summary>
        /// 支付开始时间
        /// </summary>
        public DateTime? PayStartTime { get; set; }

        /// <summary>
        /// 支付结束时间
        /// </summary>
        public DateTime? PayEndTime { get; set; }

        /// <summary>
        /// 最小金额（分）
        /// </summary>
        public int? MinAmount { get; set; }

        /// <summary>
        /// 最大金额（分）
        /// </summary>
        public int? MaxAmount { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public string? PaymentType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 微信支付响应DTO
    /// </summary>
    public class WechatPaymentResponseDto
    {
        /// <summary>
        /// 支付记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 红包记录ID
        /// </summary>
        public int RewardId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 微信OpenID
        /// </summary>
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 商户订单号
        /// </summary>
        public string OutTradeNo { get; set; } = string.Empty;

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 货币类型
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        public string? Subject { get; set; }

        /// <summary>
        /// 商品描述
        /// </summary>
        public string? Body { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public string? PaymentType { get; set; }

        /// <summary>
        /// 微信交易号
        /// </summary>
        public string? TransactionId { get; set; }

        /// <summary>
        /// 支付金额（分）
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// 支付金额（元）
        /// </summary>
        public decimal AmountYuan => Amount / 100m;

        /// <summary>
        /// 支付状态:0待支付,1支付成功,2支付失败
        /// </summary>
        public byte Status { get; set; }

        /// <summary>
        /// 支付状态名称
        /// </summary>
        public string StatusName { get; set; } = string.Empty;

        /// <summary>
        /// 支付描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 微信响应数据
        /// </summary>
        public string? WechatResponse { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PayTime { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string? FailReason { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 退款单号
        /// </summary>
        public string? RefundNo { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// 退款原因
        /// </summary>
        public string? RefundReason { get; set; }

        /// <summary>
        /// 退款时间
        /// </summary>
        public DateTime? RefundTime { get; set; }

        /// <summary>
        /// 通知URL
        /// </summary>
        public string? NotifyUrl { get; set; }

        /// <summary>
        /// 返回URL
        /// </summary>
        public string? ReturnUrl { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 微信支付统计DTO
    /// </summary>
    public class WechatPaymentStatisticsDto
    {
        /// <summary>
        /// 总支付数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功支付数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败支付数
        /// </summary>
        public int FailCount { get; set; }

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 退款次数
        /// </summary>
        public int RefundCount { get; set; }

        /// <summary>
        /// 待支付数
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 总支付金额（分）
        /// </summary>
        public long TotalAmount { get; set; }

        /// <summary>
        /// 成功支付金额（分）
        /// </summary>
        public long SuccessAmount { get; set; }

        /// <summary>
        /// 总支付金额（元）
        /// </summary>
        public decimal TotalAmountYuan => TotalAmount / 100m;

        /// <summary>
        /// 成功支付金额（元）
        /// </summary>
        public decimal SuccessAmountYuan => SuccessAmount / 100m;

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 今日支付数
        /// </summary>
        public int TodayCount { get; set; }

        /// <summary>
        /// 今日支付金额（分）
        /// </summary>
        public long TodayAmount { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// 平均金额
        /// </summary>
        public decimal AverageAmount { get; set; }

        /// <summary>
        /// 今日支付金额（元）
        /// </summary>
        public decimal TodayAmountYuan => TodayAmount / 100m;
    }
}
