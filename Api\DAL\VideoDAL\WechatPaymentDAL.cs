using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 微信支付数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class WechatPaymentDAL(MyContext context) : BaseQueryDLL<WechatPayment, WechatPaymentDAL.Queryable>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 微信支付查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 商户订单号(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? OutTradeNo { get; set; }

            /// <summary>
            /// 订单号(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? OrderNo { get; set; }

            /// <summary>
            /// 微信支付订单号(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? TransactionId { get; set; }

            /// <summary>
            /// 支付状态:0待支付,1支付成功,2支付失败
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 支付状态:0待支付,1支付成功,2支付失败
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? PayStatus { get; set; }

            /// <summary>
            /// 支付类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PaymentType { get; set; }

            /// <summary>
            /// 用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? UserId { get; set; }

            /// <summary>
            /// 最小金额
            /// </summary>
            [Query(QueryOperator.大于等于, columnName: "Amount")]
            public decimal? MinAmount { get; set; }

            /// <summary>
            /// 最大金额
            /// </summary>
            [Query(QueryOperator.小于等于, columnName: "Amount")]
            public decimal? MaxAmount { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }
        }

        /// <summary>
        /// 根据商户订单号获取支付记录
        /// </summary>
        /// <param name="outTradeNo">商户订单号</param>
        /// <returns>支付记录</returns>
        public async Task<WechatPayment?> GetByOutTradeNoAsync(string outTradeNo)
        {
            return await _context.Set<WechatPayment>()
                .FirstOrDefaultAsync(wp => wp.OutTradeNo == outTradeNo);
        }

        /// <summary>
        /// 根据微信支付订单号获取支付记录
        /// </summary>
        /// <param name="transactionId">微信支付订单号</param>
        /// <returns>支付记录</returns>
        public async Task<WechatPayment?> GetByTransactionIdAsync(string transactionId)
        {
            return await _context.Set<WechatPayment>()
                .FirstOrDefaultAsync(wp => wp.TransactionId == transactionId);
        }

        /// <summary>
        /// 检查商户订单号是否存在
        /// </summary>
        /// <param name="outTradeNo">商户订单号</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsOutTradeNoAsync(string outTradeNo)
        {
            return await _context.Set<WechatPayment>()
                .AnyAsync(wp => wp.OutTradeNo == outTradeNo);
        }

        /// <summary>
        /// 获取用户的支付记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>支付记录列表</returns>
        public async Task<List<WechatPayment>> GetUserPaymentHistoryAsync(int userId)
        {
            return await _context.Set<WechatPayment>()
                .Where(wp => wp.UserId == userId)
                .OrderByDescending(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取待支付的记录
        /// </summary>
        /// <returns>待支付记录列表</returns>
        public async Task<List<WechatPayment>> GetPendingPaymentsAsync()
        {
            return await _context.Set<WechatPayment>()
                .Where(wp => wp.Status == 0)
                .OrderBy(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取支付成功的记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>支付成功记录列表</returns>
        public async Task<List<WechatPayment>> GetSuccessPaymentsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Set<WechatPayment>().Where(wp => wp.Status == 1);

            if (startDate.HasValue)
            {
                query = query.Where(wp => wp.PayTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(wp => wp.PayTime <= endDate.Value);
            }

            return await query
                .OrderByDescending(wp => wp.PayTime)
                .ToListAsync();
        }

        /// <summary>
        /// 更新支付状态
        /// </summary>
        /// <param name="outTradeNo">商户订单号</param>
        /// <param name="payStatus">支付状态</param>
        /// <param name="transactionId">微信支付订单号</param>
        /// <param name="payTime">支付时间</param>
        /// <param name="failReason">失败原因</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdatePayStatusAsync(string outTradeNo, byte payStatus,
            string? transactionId = null, DateTime? payTime = null, string? failReason = null)
        {
            var payment = await GetByOutTradeNoAsync(outTradeNo);
            if (payment == null) return false;

            payment.Status = payStatus;
            if (!string.IsNullOrEmpty(transactionId))
            {
                payment.TransactionId = transactionId;
            }
            if (payTime.HasValue)
            {
                payment.PayTime = payTime.Value;
            }
            if (!string.IsNullOrEmpty(failReason))
            {
                payment.FailReason = failReason;
            }

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取支付统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>支付统计</returns>
        public async Task<PaymentStatistics> GetPaymentStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Set<WechatPayment>().AsQueryable();

            if (startDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime <= endDate.Value);
            }

            var totalCount = await query.CountAsync();
            var successCount = await query.CountAsync(wp => wp.Status == 1);
            var failCount = await query.CountAsync(wp => wp.Status == 2);
            var pendingCount = await query.CountAsync(wp => wp.Status == 0);

            var totalAmount = await query.SumAsync(wp => (decimal?)wp.Amount) ?? 0;
            var successAmount = await query.Where(wp => wp.Status == 1).SumAsync(wp => (decimal?)wp.Amount) ?? 0;

            return new PaymentStatistics
            {
                TotalCount = totalCount,
                SuccessCount = successCount,
                FailedCount = failCount,
                PendingCount = pendingCount,
                TotalAmount = totalAmount,
                SuccessAmount = successAmount
            };
        }

        /// <summary>
        /// 获取日期范围内的支付统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>支付统计列表</returns>
        public async Task<List<DailyPaymentStatistics>> GetDailyPaymentStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Set<WechatPayment>()
                .Where(wp => wp.CreateTime >= startDate && wp.CreateTime <= endDate)
                .GroupBy(wp => wp.CreateTime.Date)
                .Select(g => new DailyPaymentStatistics
                {
                    Date = g.Key,
                    TotalCount = g.Count(),
                    SuccessCount = g.Count(wp => wp.Status == 1),
                    TotalAmount = g.Sum(wp => wp.Amount),
                    SuccessAmount = g.Where(wp => wp.Status == 1).Sum(wp => wp.Amount),
                    UniqueUsers = g.Select(wp => wp.UserId).Distinct().Count()
                })
                .OrderBy(s => s.Date)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取微信支付记录
        /// </summary>
        /// <param name="id">支付记录ID</param>
        /// <returns>支付记录</returns>
        public async Task<WechatPayment?> GetByIdAsync(int id)
        {
            return await _context.Set<WechatPayment>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页微信支付记录列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<WechatPayment>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        }

        /// <summary>
        /// 根据订单号获取支付记录
        /// </summary>
        /// <param name="orderNo">订单号</param>
        /// <returns>支付记录</returns>
        public async Task<WechatPayment?> GetByOrderNoAsync(string orderNo)
        {
            return await _context.Set<WechatPayment>()
                .FirstOrDefaultAsync(wp => wp.OrderNo == orderNo);
        }

        /// <summary>
        /// 更新支付状态
        /// </summary>
        /// <param name="id">支付记录ID</param>
        /// <param name="status">支付状态</param>
        /// <param name="transactionId">微信交易号</param>
        /// <param name="payTime">支付时间</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdatePaymentStatusAsync(int id, byte status, string? transactionId = null, DateTime? payTime = null)
        {
            var payment = await GetByIdAsync(id);
            if (payment == null) return false;

            payment.Status = status;
            if (!string.IsNullOrEmpty(transactionId))
                payment.TransactionId = transactionId;
            if (payTime.HasValue)
                payment.PayTime = payTime.Value;

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="id">支付记录ID</param>
        /// <param name="refundNo">退款单号</param>
        /// <param name="refundAmount">退款金额</param>
        /// <param name="refundReason">退款原因</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ApplyRefundAsync(int id, string refundNo, int refundAmount, string refundReason)
        {
            var payment = await GetByIdAsync(id);
            if (payment == null) return false;

            payment.RefundNo = refundNo;
            payment.RefundAmount = refundAmount;
            payment.RefundReason = refundReason;
            payment.RefundTime = DateTime.Now;

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据用户ID获取支付记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>支付记录列表</returns>
        public async Task<List<WechatPayment>> GetByUserIdAsync(int userId)
        {
            return await _context.Set<WechatPayment>()
                .Where(wp => wp.UserId == userId)
                .OrderByDescending(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据红包记录ID获取支付记录
        /// </summary>
        /// <param name="rewardId">红包记录ID</param>
        /// <returns>支付记录</returns>
        public async Task<WechatPayment?> GetByRewardIdAsync(int rewardId)
        {
            return await _context.Set<WechatPayment>()
                .FirstOrDefaultAsync(wp => wp.RewardId == rewardId);
        }

        /// <summary>
        /// 获取用户在指定时间范围内的红包发放记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>红包发放记录列表</returns>
        public async Task<List<WechatPayment>> GetUserRewardsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Set<WechatPayment>()
                .Where(wp => wp.UserId == userId && wp.PaymentType == "REWARD");

            if (startDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime <= endDate.Value);
            }

            return await query
                .OrderByDescending(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取批次红包发放统计
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>红包发放统计</returns>
        public async Task<RewardStatistics> GetBatchRewardStatisticsAsync(int batchId)
        {
            // 这里需要关联rewards表来获取批次信息，暂时使用简化逻辑
            var rewards = await _context.Set<WechatPayment>()
                .Where(wp => wp.PaymentType == "REWARD")
                .ToListAsync();

            return new RewardStatistics
            {
                TotalCount = rewards.Count,
                SuccessCount = rewards.Count(r => r.Status == 1),
                FailCount = rewards.Count(r => r.Status == 2),
                PendingCount = rewards.Count(r => r.Status == 0),
                TotalAmount = rewards.Sum(r => r.Amount),
                SuccessAmount = rewards.Where(r => r.Status == 1).Sum(r => r.Amount),
                AverageAmount = rewards.Count > 0 ? (decimal)rewards.Average(r => r.Amount) : 0
            };
        }

        /// <summary>
        /// 获取员工红包发放统计
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>红包发放统计</returns>
        public async Task<RewardStatistics> GetEmployeeRewardStatisticsAsync(int employeeId, DateTime? startDate = null, DateTime? endDate = null)
        {
            // 这里需要关联用户表和推广关系来获取员工相关的红包发放，暂时使用简化逻辑
            var query = _context.Set<WechatPayment>()
                .Where(wp => wp.PaymentType == "REWARD");

            if (startDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime <= endDate.Value);
            }

            var rewards = await query.ToListAsync();

            return new RewardStatistics
            {
                TotalCount = rewards.Count,
                SuccessCount = rewards.Count(r => r.Status == 1),
                FailCount = rewards.Count(r => r.Status == 2),
                PendingCount = rewards.Count(r => r.Status == 0),
                TotalAmount = rewards.Sum(r => r.Amount),
                SuccessAmount = rewards.Where(r => r.Status == 1).Sum(r => r.Amount),
                AverageAmount = rewards.Count > 0 ? (decimal)rewards.Average(r => r.Amount) : 0
            };
        }

        /// <summary>
        /// 批量更新红包发放状态
        /// </summary>
        /// <param name="paymentIds">支付记录ID列表</param>
        /// <param name="status">新状态</param>
        /// <returns>更新成功的数量</returns>
        public async Task<int> BatchUpdateStatusAsync(List<int> paymentIds, byte status)
        {
            var payments = await _context.Set<WechatPayment>()
                .Where(wp => paymentIds.Contains(wp.Id))
                .ToListAsync();

            foreach (var payment in payments)
            {
                payment.Status = status;
                if (status == 1) // 发放成功
                {
                    payment.PayTime = DateTime.Now;
                }
            }

            return await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 红包发放统计类
        /// </summary>
        public class RewardStatistics
        {
            public int TotalCount { get; set; }
            public int SuccessCount { get; set; }
            public int FailCount { get; set; }
            public int PendingCount { get; set; }
            public long TotalAmount { get; set; }
            public long SuccessAmount { get; set; }
            public decimal AverageAmount { get; set; }
            public decimal SuccessRate => TotalCount > 0 ? (decimal)SuccessCount / TotalCount * 100 : 0;
        }

        /// <summary>
        /// 日支付统计类
        /// </summary>
        public class DailyPaymentStatistics
        {
            public DateTime Date { get; set; }
            public int TotalCount { get; set; }
            public int SuccessCount { get; set; }
            public decimal TotalAmount { get; set; }
            public decimal SuccessAmount { get; set; }
            public int UniqueUsers { get; set; }
        }

        /// <summary>
        /// 获取所有支付记录
        /// </summary>
        /// <returns>所有支付记录</returns>
        public async Task<List<WechatPayment>> GetAllPaymentsAsync()
        {
            return await _context.Set<WechatPayment>()
                .OrderByDescending(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据日期范围获取支付记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>支付记录列表</returns>
        public async Task<List<WechatPayment>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Set<WechatPayment>()
                .Where(wp => wp.CreateTime >= startDate && wp.CreateTime <= endDate)
                .OrderByDescending(wp => wp.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取支付统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="paymentType">支付类型（可选）</param>
        /// <returns>支付统计</returns>
        public async Task<PaymentStatistics> GetPaymentStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, string? paymentType = null)
        {
            var query = _context.Set<WechatPayment>().AsQueryable();

            if (startDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(wp => wp.CreateTime <= endDate.Value);
            }

            if (!string.IsNullOrEmpty(paymentType))
            {
                query = query.Where(wp => wp.PaymentType == paymentType);
            }

            var payments = await query.ToListAsync();

            return new PaymentStatistics
            {
                TotalCount = payments.Count,
                SuccessCount = payments.Count(p => p.Status == 1),
                FailedCount = payments.Count(p => p.Status == 2),
                PendingCount = payments.Count(p => p.Status == 0),
                TotalAmount = payments.Sum(p => p.Amount) / 100m, // 转换为元
                SuccessAmount = payments.Where(p => p.Status == 1).Sum(p => p.Amount) / 100m,
                UniqueUsers = payments.Select(p => p.UserId).Distinct().Count()
            };
        }

        /// <summary>
        /// 支付统计数据类
        /// </summary>
        public class PaymentStatistics
        {
            public int TotalCount { get; set; }
            public int SuccessCount { get; set; }
            public int FailedCount { get; set; }
            public int PendingCount { get; set; }
            public decimal TotalAmount { get; set; }
            public decimal SuccessAmount { get; set; }
            public int UniqueUsers { get; set; }
            public decimal SuccessRate => TotalCount > 0 ? (decimal)SuccessCount / TotalCount * 100 : 0;
            public decimal AverageAmount => TotalCount > 0 ? TotalAmount / TotalCount : 0;
        }
    }
}
