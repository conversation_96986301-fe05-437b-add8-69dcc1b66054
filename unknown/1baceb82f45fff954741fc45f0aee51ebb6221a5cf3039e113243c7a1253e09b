﻿using BLL.SysService;
using Common;
using Common.Exceptions;
using Common.JWT;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Security.Claims;
using System.Text;

namespace ServiceVideoSharing.Controllers.BasisController
{
    [Route("api/[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        /// <summary>
        /// 用户缓存键前缀
        /// </summary>
        private const string UserCacheKeyPrefix = "UserInfo_";

        /// <summary>
        /// 用户缓存过期时间
        /// </summary>
        private static readonly TimeSpan UserCacheExpiration = TimeSpan.FromMinutes(30);

        /// <summary>
        /// 当前请求的IP
        /// </summary>
        public string IP
        {
            get
            {
                // 先尝试从X-Forwarded-For获取
                string forwardedIp = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault() ?? string.Empty;
                if (!string.IsNullOrEmpty(forwardedIp))
                {
                    // 获取第一个IP地址（最初的客户端）
                    string clientIp = forwardedIp.Split(',')[0].Trim();
                    if (IPAddress.TryParse(clientIp, out _)) return clientIp;

                }

                // 再尝试从RemoteIpAddress获取
                string remoteIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "未知IP";
                return remoteIp;
            }
        }

        /// <summary>
        /// 获取请求头信息
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        protected string GetHeader(string key)
        => HttpContext.Request.Headers[key].ToString() ?? throw new InvalidOperationException($"Header '{key}' not found.");

        /// <summary>
        /// 尝试获取请求头信息，如果不存在则返回默认值
        /// </summary>
        /// <param name="key">请求头名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>请求头值或默认值</returns>
        protected string TryGetHeader(string key, string defaultValue = "")
        {
            try
            {
                return HttpContext.Request.Headers[key].ToString() ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 验证并解析JWT Token
        /// </summary>
        /// <returns>ClaimsPrincipal对象</returns>
        /// <exception cref="AuthorizationException">当token验证失败时抛出</exception>
        protected ClaimsPrincipal ValidateToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = JWTSetting.Sign;
                if (string.IsNullOrEmpty(key))
                    throw new AuthorizationException("未配置JWT签名密钥");

                var keyBytes = Encoding.ASCII.GetBytes(key);
                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(keyBytes),
                    ValidateIssuer = true,
                    ValidIssuer = JWTSetting.Issuer,
                    ValidateAudience = true,
                    ValidAudience = JWTSetting.Audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                return tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
            }
            catch (SecurityTokenExpiredException)
            {
                throw new AuthorizationException("token已过期");
            }
            catch (SecurityTokenException)
            {
                throw new AuthorizationException("无效的token");
            }
            catch (Exception)
            {
                throw new AuthorizationException("token验证失败");
            }
        }

        /// <summary>
        /// 从JWT中获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        /// <exception cref="AuthorizationException">当无法获取用户ID时抛出</exception>
        protected string GetCurrentUserId()
        {
            return GetCurrentUserInfo().UserId;
        }

        /// <summary>
        /// 获取当前用户信息（包含用户ID、用户名和用户类型）
        /// </summary>
        protected CurrentUserInfoDto GetCurrentUserInfo()
        {
            var jwtUserInfo = GetCurrentJwtUserInfo();
            return new CurrentUserInfoDto
            {
                UserId = jwtUserInfo.UserId,
                UserName = jwtUserInfo.UserName,
                UserType = jwtUserInfo.UserType
            };
        }

        /// <summary>
        /// 从JWT Token中获取完整的用户信息
        /// </summary>
        /// <returns>JWT中的用户信息</returns>
        /// <exception cref="AuthorizationException">当无法获取用户信息时抛出</exception>
        protected Common.JWT.UserInfo GetCurrentJwtUserInfo()
        {
            try
            {
                var token = GetJwtToken();
                if (string.IsNullOrEmpty(token))
                    throw new AuthorizationException("未找到有效的JWT令牌");

                return JWTHelper.GetUserInfo(token);
            }
            catch (AuthorizationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new AuthorizationException($"获取用户信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前用户类型
        /// </summary>
        /// <returns>用户类型：1-超级管理员，2-管理员，3-员工</returns>
        protected byte GetCurrentUserType()
        {
            var userInfo = GetCurrentJwtUserInfo();
            return userInfo.UserType;
        }

        /// <summary>
        /// 检查当前用户是否为超级管理员
        /// </summary>
        /// <returns>是否为超级管理员</returns>
        protected bool IsSuperAdmin()
        {
            return GetCurrentUserInfo().UserType == 1;
        }

        /// <summary>
        /// 检查当前用户是否为管理员（包括超级管理员）
        /// </summary>
        /// <returns>是否为管理员</returns>
        protected bool IsAdminUser()
        {
            var userType = GetCurrentUserInfo().UserType;
            return userType == 1 || userType == 2;
        }

        /// <summary>
        /// 检查当前用户是否为员工
        /// </summary>
        /// <returns>是否为员工</returns>
        protected bool IsEmployee()
        {
            return GetCurrentUserInfo().UserType == 3;
        }

        /// <summary>
        /// 获取当前用户ID（整数类型）
        /// </summary>
        /// <returns>用户ID的整数值</returns>
        /// <exception cref="AuthorizationException">当无法获取或转换用户ID时抛出</exception>
        protected int GetCurrentUserIdAsInt()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (int.TryParse(userId, out int result))
                    return result;

                throw new AuthorizationException($"用户ID格式无效: {userId}");
            }
            catch (AuthorizationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new AuthorizationException($"获取用户ID失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从JWT中获取当前用户名
        /// </summary>
        /// <returns>用户名</returns>
        /// <exception cref="AuthorizationException">当无法获取用户名时抛出</exception>
        protected string GetCurrentUserName()
        {
            return GetCurrentUserInfo().UserName;
        }

        /// <summary>
        /// 检查当前用户是否为管理员
        /// </summary>
        /// <returns>是否为管理员</returns>
        protected bool IsAdmin()
        {
            try
            {
                var userName = GetCurrentUserName();
                return userName.Equals("admin", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取指定类型的声明值
        /// </summary>
        /// <param name="claimType">声明类型</param>
        /// <returns>声明值</returns>
        /// <exception cref="AuthorizationException">当无法获取指定声明时抛出</exception>
        protected string GetClaimValue(string claimType)
        {

            // 获取token
            var token = GetJwtToken();
            if (string.IsNullOrEmpty(token))
                throw new AuthorizationException("未找到有效的JWT令牌");

            // 解析token
            var payload = JWTHelper.ValidateJwtToken(token);
            var jwt = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(payload);

            if (jwt != null && jwt.TryGetValue(claimType, out var claimValueObj))
            {
                var claimValue = claimValueObj.ToString();
                if (!string.IsNullOrWhiteSpace(claimValue))
                {
                    return claimValue;
                }
            }

            // 如果从JWT无法获取，尝试从User对象获取
            var claim = User?.FindFirst(claimType);
            if (claim == null || string.IsNullOrWhiteSpace(claim.Value))
                throw new AuthorizationException($"未找到有效的{claimType}声明");

            return claim.Value;
        }

        /// <summary>
        /// 尝试获取指定类型的声明值，失败时返回默认值而不抛出异常
        /// </summary>
        /// <param name="claimType">声明类型</param>
        /// <param name="defaultValue">获取失败时返回的默认值</param>
        /// <returns>声明值或默认值</returns>
        protected string TryGetClaimValue(string claimType, string defaultValue = "")
        {
            try
            {
                return GetClaimValue(claimType);
            }
            catch
            {
                return defaultValue;
            }
        }

        ///// <summary>
        ///// 从JWT中获取当前用户信息
        ///// </summary>
        ///// <param name="userService">用户服务</param>
        ///// <param name="useCache">是否使用缓存</param>
        ///// <returns>用户信息DTO</returns>
        ///// <exception cref="ArgumentNullException">当用户服务为空时抛出</exception>
        ///// <exception cref="AuthorizationException">当无法获取用户ID时抛出</exception>
        //protected async Task<UserInfoDto> GetCurrentUserInfoAsync(SysUserService userService, bool useCache = true)
        //{
        //    if (userService == null)
        //        throw new ArgumentNullException(nameof(userService), "用户服务不能为空");

        //    string userId = GetCurrentUserId();

        //    // 如果启用缓存，尝试从Redis缓存获取用户信息
        //    if (useCache)
        //    {
        //        string cacheKey = $"{UserCacheKeyPrefix}{userId}";

        //        // 从Redis缓存中获取用户信息
        //        var cachedUserInfo = RedisHelper.Get<UserInfoDto>(cacheKey);
        //        if (cachedUserInfo != null)
        //            return cachedUserInfo;

        //        // 从服务获取用户信息
        //        var userInfo = await userService.GetUserInfoAsync(userId) ?? throw new AuthorizationException($"用户不存在: {userId}");

        //        // 将用户信息存入Redis缓存
        //        RedisHelper.Set(cacheKey, userInfo, UserCacheExpiration);
        //        return userInfo;
        //    }

        //    // 不使用缓存，直接从服务获取
        //    var result = await userService.GetUserInfoAsync(userId);
        //    return result ?? throw new AuthorizationException($"用户不存在: {userId}");
        //}


        /// <summary>
        /// 从JWT中获取当前用户信息
        /// </summary>
        /// <param name="userService">用户服务</param>
        /// <returns>用户信息DTO</returns>
        /// <exception cref="ArgumentNullException">当用户服务为空时抛出</exception>
        /// <exception cref="AuthorizationException">当无法获取用户ID时抛出</exception>
        protected async Task<SysUserInfoDto> GetCurrentUserInfoAsync(SysUserService userService)
        {
            if (userService == null)
                throw new ArgumentNullException(nameof(userService), "用户服务不能为空");

            string userId = GetCurrentUserId();

            var result = await userService.GetUserInfoAsync(userId);
            return result ?? throw new AuthorizationException($"用户不存在: {userId}");
        }



        /// <summary>
        /// 获取JWT令牌
        /// </summary>
        /// <returns>JWT令牌字符串</returns>
        private string GetJwtToken()
        {
            var authHeader = HttpContext.Request.Headers.Authorization.ToString();
            if (string.IsNullOrEmpty(authHeader))
                return string.Empty;

            // Bearer token格式
            if (authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                return authHeader[7..].Trim();

            return authHeader.Trim();
        }





        #region 返回结果

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="data">响应数据</param>
        /// <param name="message">响应消息</param>
        /// <returns>成功响应</returns>
        protected Result<T> Success<T>(T data, string message = "操作成功")
        => new()
        {
            Code = 200,
            Success = true,
            Msg = message,
            Data = data
        };

        /// <summary>
        /// 创建成功响应（无数据）
        /// </summary>
        /// <param name="message">响应消息</param>
        /// <returns>成功响应</returns>
        protected Result Success(string message = "操作成功")
        => new()
        {
            Code = 200,
            Success = true,
            Msg = message
        };

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <typeparam name="T">响应数据类型</typeparam>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误码</param>
        /// <returns>失败响应</returns>
        protected Result<T> Fail<T>(string message, int code = 500)
        => new()
        {
            Code = code,
            Success = false,
            Msg = message
        };

        /// <summary>
        /// 创建失败响应（无数据）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误码</param>
        /// <returns>失败响应</returns>
        protected Result Fail(string message, int code = 500)
        => new()
        {
            Code = code,
            Success = false,
            Msg = message
        };


        /// <summary>
        /// 创建BadRequest响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BadRequest响应</returns>
        protected IActionResult BadRequestResult(string message)
        => BadRequest(Fail(message, 400));

        /// <summary>
        /// 创建NotFound响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>NotFound响应</returns>
        protected IActionResult NotFoundResult(string message)
        => NotFound(Fail(message, 404));

        /// <summary>
        /// 创建Unauthorized响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>Unauthorized响应</returns>
        protected IActionResult UnauthorizedResult(string message = "未授权访问")
        => Unauthorized(Fail(message, 401));

        /// <summary>
        /// 创建Forbidden响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>Forbidden响应</returns>
        protected IActionResult ForbiddenResult(string message = "禁止访问")
        => StatusCode(403, Fail(message, 403));

        #endregion


    }
}
