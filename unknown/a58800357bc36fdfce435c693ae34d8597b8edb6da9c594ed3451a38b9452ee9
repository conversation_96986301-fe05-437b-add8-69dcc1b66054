using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;
using static DAL.SysDAL.SysUserDAL;

namespace DAL.SysDAL
{
    /// <summary>
    /// 用户数据访问层实现类
    /// 负责用户相关的数据库操作
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    [Dependency(DependencyType.Scoped)]
    public class SysUserDAL(MyContext context) : BaseQueryDLL<SysUser, UserDALQuery>(context)
    {
        /// <summary>
        /// 数据库上下文实例
        /// </summary>
        private readonly MyContext _context = context;

        /// <summary>
        /// 用户查询条件模型类
        /// 定义了用户查询时可用的过滤条件
        /// </summary>
        public class UserDALQuery : PageQueryEntity
        {
            /// <summary>
            /// 用户ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 用户名
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? UserName { get; set; }

            /// <summary>
            /// 真实姓名
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? RealName { get; set; }

            /// <summary>
            /// 手机号
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Mobile { get; set; }

            /// <summary>
            /// 电子邮箱
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Email { get; set; }

            /// <summary>
            /// 用户状态
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 用户类型
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? UserType { get; set; }

            /// <summary>
            /// 创建时间范围开始
            /// </summary>
            [Query(QueryOperator.日期范围, columnName: "CreateTime", relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 创建时间范围结束
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 创建时间排序标志
            /// 用于按创建时间降序排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "CreateTime", orderDirection: OrderDirection.降序, orderPriority: 1)]
            public int? OrderByCreateTime { get; set; }
        }

        /// <summary>
        /// 根据用户ID获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息，如果不存在返回null</returns>
        public async Task<SysUser?> GetByIdAsync(string userId)
        {
            return await _context.SysUsers.FirstOrDefaultAsync(u => u.UserId == userId);
        }



        /// <summary>
        /// 根据用户ID列表获取用户列表
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户列表</returns>
        public async Task<List<SysUser>> GetUsersByIdsAsync(List<string> userIds)
        {
            return await _context.SysUsers
                .Where(u => userIds.Contains(u.UserId))
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户查询对象
        /// </summary>
        /// <returns>查询对象</returns>
        public IQueryable<SysUser> GetQueryable()
        => _context.SysUsers.AsQueryable();

        /// <summary>
        /// 检查用户名是否已存在
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns>存在返回true,不存在返回false</returns>
        public Task<bool> ExistsUserNameAsync(string userName, string? excludeUserId = null)
        {
            var query = _context.SysUsers.AsQueryable();
            if (!string.IsNullOrEmpty(excludeUserId))
                query = query.Where(u => u.UserId != excludeUserId);

            return query.AnyAsync(u => u.UserName == userName);
        }



        /// <summary>
        /// 检查用户信息唯一性（用户名、邮箱、手机号）
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="email">邮箱</param>
        /// <param name="mobile">手机号</param>
        /// <param name="excludeUserId">排除的用户ID，用于更新时检查</param>
        /// <returns>
        /// 返回存在重复的字段名和值的字典。
        /// 如果字典为空，表示没有重复；否则，字典包含重复的字段名和值。
        /// </returns>
        public async Task<Dictionary<string, string>> CheckUserInfoUniqueAsync(
            string? userName,
            string? email,
            string? mobile,
            string? excludeUserId = null)
        {
            var result = new Dictionary<string, string>();
            var query = _context.SysUsers.AsQueryable();

            // 排除当前用户
            if (!string.IsNullOrEmpty(excludeUserId))
                query = query.Where(u => u.UserId != excludeUserId);

            // 执行一次查询获取所有可能重复的用户
            var existingUsers = await query
                .Where(u => (!string.IsNullOrEmpty(userName) && u.UserName == userName) ||
                            (!string.IsNullOrEmpty(email) && u.Email == email) ||
                            (!string.IsNullOrEmpty(mobile) && u.Mobile == mobile))
                .Select(u => new { u.UserName, u.Email, u.Mobile })
                .ToListAsync();

            // 检查用户名是否重复
            if (!string.IsNullOrEmpty(userName) && existingUsers.Any(u => u.UserName == userName))
                result.Add("UserName", userName);

            // 检查邮箱是否重复
            if (!string.IsNullOrEmpty(email) && existingUsers.Any(u => u.Email == email))
                result.Add("Email", email);

            // 检查手机号是否重复
            if (!string.IsNullOrEmpty(mobile) && existingUsers.Any(u => u.Mobile == mobile))
                result.Add("Mobile", mobile);

            return result;
        }






        /// <summary>
        /// 获取分页数据列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="queryable">查询条件模型</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysUser>> GetPageDataAsync(
            UserDALQuery queryable)
        => GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        /// <summary>
        /// 根据用户类型获取用户列表
        /// </summary>
        /// <param name="userType">用户类型：1-超级管理员，2-管理员，3-员工</param>
        /// <returns>用户列表</returns>
        public async Task<List<SysUser>> GetByUserTypeAsync(byte userType)
        {
            return await _context.SysUsers
                .Where(u => u.UserType == userType)
                .OrderByDescending(u => u.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据上级用户ID获取直接下级用户列表
        /// </summary>
        /// <param name="parentUserId">上级用户ID</param>
        /// <returns>直接下级用户列表</returns>
        public async Task<List<SysUser>> GetByParentUserIdAsync(string parentUserId)
        {
            return await _context.SysUsers
                .Where(u => u.ParentUserId == parentUserId)
                .OrderByDescending(u => u.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据员工ID获取其管理的普通用户列表
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>普通用户列表</returns>
        public async Task<List<Entity.Entitys.VideoEntity.User>> GetSubordinateUsersAsync(string employeeId)
        {
            return await _context.Users
                .Where(u => u.EmployeeId == employeeId)
                .OrderByDescending(u => u.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户的所有下级用户ID列表（递归获取）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>所有下级用户ID列表</returns>
        public async Task<List<string>> GetAllSubordinateUserIdsAsync(string userId)
        {
            var result = new List<string>();
            var directSubordinates = await GetByParentUserIdAsync(userId);

            foreach (var subordinate in directSubordinates)
            {
                result.Add(subordinate.UserId);
                // 递归获取下级的下级
                var subSubordinates = await GetAllSubordinateUserIdsAsync(subordinate.UserId);
                result.AddRange(subSubordinates);
            }

            return result;
        }

        /// <summary>
        /// 获取用户管理的所有普通用户ID列表（包括间接管理）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>所有管理的普通用户ID列表</returns>
        public async Task<List<int>> GetAllManagedUserIdsAsync(string userId)
        {
            var result = new List<int>();
            var user = await GetByIdAsync(userId);

            if (user == null) return result;

            // 如果是员工，直接获取其管理的用户
            if (user.UserType == 3)
            {
                var users = await GetSubordinateUsersAsync(userId);
                result.AddRange(users.Select(u => u.Id));
            }
            else
            {
                // 如果是管理员或超级管理员，需要获取所有下级员工管理的用户
                var allSubordinateIds = await GetAllSubordinateUserIdsAsync(userId);

                foreach (var subordinateId in allSubordinateIds)
                {
                    var subordinateUser = await GetByIdAsync(subordinateId);
                    if (subordinateUser?.UserType == 3) // 如果是员工
                    {
                        var users = await GetSubordinateUsersAsync(subordinateId);
                        result.AddRange(users.Select(u => u.Id));
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取用户的直接下级数量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>直接下级数量</returns>
        public async Task<int> GetDirectSubordinateCountAsync(string userId)
        {
            return await _context.SysUsers
                .CountAsync(u => u.ParentUserId == userId);
        }

        /// <summary>
        /// 获取员工管理的普通用户数量
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>管理的普通用户数量</returns>
        public async Task<int> GetManagedUserCountAsync(string employeeId)
        {
            return await _context.Users
                .CountAsync(u => u.EmployeeId == employeeId);
        }





    }
}