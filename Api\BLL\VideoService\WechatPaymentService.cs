using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;

namespace BLL.VideoService
{
    /// <summary>
    /// 微信支付业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class WechatPaymentService(WechatPaymentDAL wechatPaymentDAL, UserDAL userDAL, SysLogService logService)
    {
        private readonly WechatPaymentDAL _wechatPaymentDAL = wechatPaymentDAL;
        private readonly UserDAL _userDAL = userDAL;
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 创建支付订单
        /// </summary>
        /// <param name="createDto">创建支付订单DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>支付订单ID</returns>
        public async Task<int> CreatePaymentOrderAsync(WechatPaymentCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户是否存在
            _ = await _userDAL.GetByIdAsync(createDto.UserId) ?? throw new BusinessException("指定的用户不存在");

            // 验证金额
            if (createDto.Amount <= 0)
                throw new BusinessException("支付金额必须大于0");

            // 生成订单号
            var orderNo = GenerateOrderNo();

            // 创建微信支付实体
            var wechatPayment = new WechatPayment
            {
                UserId = createDto.UserId,
                OrderNo = orderNo,
                Amount = (int)(createDto.Amount * 100),
                Currency = createDto.Currency ?? "CNY",
                Subject = createDto.Subject,
                Body = createDto.Body,
                PaymentType = createDto.PaymentType,
                Status = 0, // 待支付
                NotifyUrl = createDto.NotifyUrl,
                ReturnUrl = createDto.ReturnUrl,
                CreateTime = DateTime.Now
            };

            // 添加支付订单
            await _wechatPaymentDAL.AddAsync(wechatPayment);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信支付",
                Operation = "创建支付订单",
                BusinessObject = "WechatPayment",
                ObjectId = wechatPayment.Id.ToString(),
                DetailedInfo = $"用户 {createDto.UserId} 创建支付订单，金额：{createDto.Amount}元",
                AfterData = new { wechatPayment.OrderNo, wechatPayment.Amount, wechatPayment.Subject },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return wechatPayment.Id;
        }

        /// <summary>
        /// 更新支付状态
        /// </summary>
        /// <param name="orderNo">订单号</param>
        /// <param name="status">支付状态</param>
        /// <param name="transactionId">微信交易号</param>
        /// <param name="payTime">支付时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdatePaymentStatusAsync(string orderNo, byte status, string? transactionId = null, DateTime? payTime = null, CurrentUserInfoDto? currentUserInfo = null)
        {
            var payment = await _wechatPaymentDAL.GetByOrderNoAsync(orderNo)
                ?? throw new BusinessException("支付订单不存在");

            var beforeStatus = payment.Status;
            var result = await _wechatPaymentDAL.UpdatePaymentStatusAsync(payment.Id, status, transactionId, payTime);

            // 记录业务日志
            if (currentUserInfo != null)
            {
                var statusText = status switch
                {
                    1 => "支付成功",
                    2 => "支付失败",
                    3 => "已退款",
                    4 => "已取消",
                    _ => "未知状态"
                };

                await _logService.LogBusinessOperationAsync(new BusinessLogDto
                {
                    Module = "微信支付",
                    Operation = "更新支付状态",
                    BusinessObject = "WechatPayment",
                    ObjectId = payment.Id.ToString(),
                    DetailedInfo = $"订单 {orderNo} 状态更新为：{statusText}",
                    BeforeData = new { Status = beforeStatus },
                    AfterData = new { Status = status, TransactionId = transactionId, PayTime = payTime },
                    UserId = currentUserInfo.UserId,
                    Username = currentUserInfo.UserName,
                    Level = LogLevel.Information
                });
            }

            return result;
        }

        /// <summary>
        /// 处理支付回调
        /// </summary>
        /// <param name="callbackDto">支付回调DTO</param>
        /// <returns>是否成功</returns>
        public async Task<bool> HandlePaymentCallbackAsync(WechatPaymentCallbackDto callbackDto)
        {
            var payment = await _wechatPaymentDAL.GetByOrderNoAsync(callbackDto.OrderNo ?? string.Empty)
                ?? throw new BusinessException("支付订单不存在");

            // 验证金额 - 转换为分
            int amountInCents = (int)(callbackDto.Amount * 100);
            if (amountInCents != payment.Amount)
                throw new BusinessException("支付金额不匹配");

            // 更新支付状态
            var status = callbackDto.ResultCode == "SUCCESS" ? (byte)1 : (byte)2;
            var result = await _wechatPaymentDAL.UpdatePaymentStatusAsync(
                payment.Id,
                status,
                callbackDto.TransactionId,
                callbackDto.PayTime);

            // 如果支付成功，执行后续业务逻辑
            if (status == 1)
            {
                await ProcessSuccessfulPaymentAsync(payment);
            }

            return result;
        }

        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="refundDto">退款申请DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ApplyRefundAsync(WechatPaymentRefundDto refundDto, CurrentUserInfoDto currentUserInfo)
        {
            var payment = await _wechatPaymentDAL.GetByOrderNoAsync(refundDto.OrderNo ?? string.Empty)
                ?? throw new BusinessException("支付订单不存在");

            if (payment.Status != 1)
                throw new BusinessException("只有支付成功的订单才能申请退款");

            // 转换为分
            int refundAmountInCents = (int)(refundDto.RefundAmount * 100);
            if (refundAmountInCents > payment.Amount)
                throw new BusinessException("退款金额不能大于支付金额");

            // 生成退款单号
            var refundNo = GenerateRefundNo();

            var result = await _wechatPaymentDAL.ApplyRefundAsync(
                payment.Id,
                refundNo,
                refundAmountInCents,
                refundDto.RefundReason);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信支付",
                Operation = "申请退款",
                BusinessObject = "WechatPayment",
                ObjectId = payment.Id.ToString(),
                DetailedInfo = $"订单 {refundDto.OrderNo} 申请退款，金额：{refundDto.RefundAmount}元",
                AfterData = new { RefundNo = refundNo, refundDto.RefundAmount, refundDto.RefundReason },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 获取支付订单详情
        /// </summary>
        /// <param name="paymentId">支付订单ID</param>
        /// <returns>支付订单响应DTO</returns>
        public async Task<WechatPaymentResponseDto?> GetPaymentOrderAsync(int paymentId)
        {
            var wechatPayment = await _wechatPaymentDAL.GetByIdAsync(paymentId);
            if (wechatPayment == null) return null;

            return new WechatPaymentResponseDto
            {
                Id = wechatPayment.Id,
                UserId = wechatPayment.UserId,
                OrderNo = wechatPayment.OrderNo,
                Amount = wechatPayment.Amount, // Amount字段是int类型（分）
                Currency = wechatPayment.Currency,
                Subject = wechatPayment.Subject,
                Body = wechatPayment.Body,
                PaymentType = wechatPayment.PaymentType,
                Status = wechatPayment.Status,
                TransactionId = wechatPayment.TransactionId,
                PayTime = wechatPayment.PayTime,
                RefundNo = wechatPayment.RefundNo,
                RefundAmount = wechatPayment.RefundAmount.HasValue ? wechatPayment.RefundAmount.Value / 100m : 0m,
                RefundReason = wechatPayment.RefundReason,
                RefundTime = wechatPayment.RefundTime,
                NotifyUrl = wechatPayment.NotifyUrl,
                ReturnUrl = wechatPayment.ReturnUrl,
                CreateTime = wechatPayment.CreateTime
            };
        }

        /// <summary>
        /// 根据订单号获取支付订单
        /// </summary>
        /// <param name="orderNo">订单号</param>
        /// <returns>支付订单响应DTO</returns>
        public async Task<WechatPaymentResponseDto?> GetPaymentOrderByOrderNoAsync(string orderNo)
        {
            var wechatPayment = await _wechatPaymentDAL.GetByOrderNoAsync(orderNo);
            if (wechatPayment == null) return null;

            return new WechatPaymentResponseDto
            {
                Id = wechatPayment.Id,
                UserId = wechatPayment.UserId,
                OrderNo = wechatPayment.OrderNo,
                Amount = wechatPayment.Amount, // Amount字段是int类型（分）
                Currency = wechatPayment.Currency,
                Subject = wechatPayment.Subject,
                Body = wechatPayment.Body,
                PaymentType = wechatPayment.PaymentType,
                Status = wechatPayment.Status,
                TransactionId = wechatPayment.TransactionId,
                PayTime = wechatPayment.PayTime,
                RefundNo = wechatPayment.RefundNo,
                RefundAmount = wechatPayment.RefundAmount.HasValue ? wechatPayment.RefundAmount.Value / 100m : 0m,
                RefundReason = wechatPayment.RefundReason,
                RefundTime = wechatPayment.RefundTime,
                NotifyUrl = wechatPayment.NotifyUrl,
                ReturnUrl = wechatPayment.ReturnUrl,
                CreateTime = wechatPayment.CreateTime
            };
        }

        /// <summary>
        /// 获取分页支付订单列表
        /// </summary>
        /// <param name="queryDto">查询DTO</param>
        /// <returns>分页支付订单列表</returns>
        public async Task<PagedResult<WechatPaymentResponseDto>> GetPaymentOrderPagedListAsync(WechatPaymentQueryDto queryDto)
        {
            var queryable = new WechatPaymentDAL.Queryable
            {
                OrderNo = queryDto.OrderNo,
                TransactionId = queryDto.TransactionId,
                Status = queryDto.Status,
                PaymentType = queryDto.PaymentType,
                UserId = queryDto.UserId,
                MinAmount = queryDto.MinAmount.HasValue ? queryDto.MinAmount.Value : null,
                MaxAmount = queryDto.MaxAmount.HasValue ? queryDto.MaxAmount.Value : null,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var pageEntity = await _wechatPaymentDAL.GetPagedListAsync(queryable);

            var items = (pageEntity.List ?? []).Select(wp => new WechatPaymentResponseDto
            {
                Id = wp.Id,
                UserId = wp.UserId,
                OrderNo = wp.OrderNo,
                Amount = wp.Amount, // Amount字段是int类型（分）
                Currency = wp.Currency,
                Subject = wp.Subject,
                Body = wp.Body,
                PaymentType = wp.PaymentType,
                Status = wp.Status,
                TransactionId = wp.TransactionId,
                PayTime = wp.PayTime,
                RefundNo = wp.RefundNo,
                RefundAmount = wp.RefundAmount.HasValue ? wp.RefundAmount.Value / 100m : 0m,
                RefundReason = wp.RefundReason,
                RefundTime = wp.RefundTime,
                NotifyUrl = wp.NotifyUrl,
                ReturnUrl = wp.ReturnUrl,
                CreateTime = wp.CreateTime
            }).ToList();

            return new PagedResult<WechatPaymentResponseDto>
            {
                Items = items,
                TotalCount = pageEntity.TotalCount,
                PageIndex = pageEntity.PageIndex,
                PageSize = pageEntity.PageSize
            };
        }

        /// <summary>
        /// 获取用户支付订单列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">支付状态</param>
        /// <returns>用户支付订单列表</returns>
        public async Task<List<WechatPaymentResponseDto>> GetUserPaymentOrdersAsync(int userId, byte? status = null)
        {
            var payments = await _wechatPaymentDAL.GetByUserIdAsync(userId);
            if (status.HasValue)
            {
                payments = [.. payments.Where(p => p.Status == status.Value)];
            }

            return [.. payments.Select(wp => new WechatPaymentResponseDto
            {
                Id = wp.Id,
                UserId = wp.UserId,
                OrderNo = wp.OrderNo,
                Amount = wp.Amount, // Amount字段是int类型（分）
                Currency = wp.Currency,
                Subject = wp.Subject,
                Body = wp.Body,
                PaymentType = wp.PaymentType,
                Status = wp.Status,
                TransactionId = wp.TransactionId,
                PayTime = wp.PayTime,
                RefundNo = wp.RefundNo,
                RefundAmount = wp.RefundAmount.HasValue ? wp.RefundAmount.Value / 100m : 0m,
                RefundReason = wp.RefundReason,
                RefundTime = wp.RefundTime,
                NotifyUrl = wp.NotifyUrl,
                ReturnUrl = wp.ReturnUrl,
                CreateTime = wp.CreateTime
            })];
        }

        /// <summary>
        /// 获取红包发放统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="userId">用户ID</param>
        /// <returns>红包发放统计信息</returns>
        public async Task<WechatPaymentStatisticsDto> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate, int? userId = null)
        {
            var statistics = await _wechatPaymentDAL.GetPaymentStatisticsAsync(startDate, endDate);

            return new WechatPaymentStatisticsDto
            {
                TotalCount = statistics.TotalCount,
                SuccessCount = statistics.SuccessCount,
                FailCount = statistics.FailedCount,
                PendingCount = statistics.PendingCount,
                TotalAmount = (long)statistics.TotalAmount, // 转换为long类型（分）
                SuccessAmount = (long)statistics.SuccessAmount, // 转换为long类型（分）
                SuccessRate = statistics.SuccessRate
            };
        }

        /// <summary>
        /// 发放红包给用户
        /// 用户完成视频观看和答题后调用此方法发放红包
        /// </summary>
        /// <param name="rewardId">红包记录ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="openId">用户微信OpenID</param>
        /// <param name="amount">红包金额（元）</param>
        /// <param name="description">红包描述</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>支付记录ID</returns>
        public async Task<int> SendRewardToUserAsync(int rewardId, int userId, string openId, decimal amount, string description, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户是否存在
            _ = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException("指定的用户不存在");

            // 验证金额
            if (amount <= 0)
                throw new BusinessException("红包金额必须大于0");

            // 生成商户订单号
            var outTradeNo = GenerateOutTradeNo();

            // 创建微信支付实体
            var wechatPayment = new WechatPayment
            {
                RewardId = rewardId,
                UserId = userId,
                OpenId = openId,
                OutTradeNo = outTradeNo,
                OrderNo = GenerateOrderNo(),
                Amount = (int)(amount * 100), // 转换为分
                Currency = "CNY",
                Subject = "视频观看红包奖励",
                Body = description,
                PaymentType = "REWARD",
                Status = 0, // 待发放
                Description = description,
                CreateTime = DateTime.Now
            };

            // 添加支付记录
            await _wechatPaymentDAL.AddAsync(wechatPayment);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信红包",
                Operation = "发放红包",
                BusinessObject = "WechatPayment",
                ObjectId = wechatPayment.Id.ToString(),
                DetailedInfo = $"向用户 {userId} 发放红包，金额：{amount}元，描述：{description}",
                AfterData = new { wechatPayment.OutTradeNo, wechatPayment.Amount, wechatPayment.Description },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return wechatPayment.Id;
        }

        /// <summary>
        /// 批量发放红包
        /// </summary>
        /// <param name="rewardRequests">红包发放请求列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>成功发放的数量</returns>
        public async Task<int> BatchSendRewardsAsync(List<RewardRequest> rewardRequests, CurrentUserInfoDto currentUserInfo)
        {
            int successCount = 0;
            foreach (var request in rewardRequests)
            {
                try
                {
                    await SendRewardToUserAsync(request.RewardId, request.UserId, request.OpenId, request.Amount, request.Description, currentUserInfo);
                    successCount++;
                }
                catch (Exception ex)
                {
                    // 记录失败日志
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "微信红包",
                        Operation = "批量发放红包失败",
                        BusinessObject = "WechatPayment",
                        ObjectId = request.RewardId.ToString(),
                        DetailedInfo = $"向用户 {request.UserId} 发放红包失败：{ex.Message}",
                        UserId = currentUserInfo.UserId,
                        Username = currentUserInfo.UserName,
                        Level = LogLevel.Error
                    });
                }
            }
            return successCount;
        }

        /// <summary>
        /// 获取失败的红包发放记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>失败的红包发放记录列表</returns>
        public async Task<List<WechatPaymentResponseDto>> GetFailedRewardsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var queryable = new WechatPaymentDAL.Queryable
            {
                Status = 2, // 发放失败
                StartTime = startDate,
                EndTime = endDate,
                PageIndex = 1,
                PageSize = 1000 // 获取大量数据
            };

            var pageEntity = await _wechatPaymentDAL.GetPagedListAsync(queryable);
            return [.. (pageEntity.List ?? []).Select(ConvertToResponseDto)];
        }

        /// <summary>
        /// 获取待发放的红包记录
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>待发放的红包记录列表</returns>
        public async Task<List<WechatPaymentResponseDto>> GetPendingRewardsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var queryable = new WechatPaymentDAL.Queryable
            {
                Status = 0, // 待发放
                StartTime = startDate,
                EndTime = endDate,
                PageIndex = 1,
                PageSize = 1000 // 获取大量数据
            };

            var pageEntity = await _wechatPaymentDAL.GetPagedListAsync(queryable);
            return [.. (pageEntity.List ?? []).Select(ConvertToResponseDto)];
        }

        /// <summary>
        /// 重新发放失败的红包
        /// </summary>
        /// <param name="paymentId">支付记录ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> RetryFailedRewardAsync(int paymentId, CurrentUserInfoDto currentUserInfo)
        {
            var payment = await _wechatPaymentDAL.GetByIdAsync(paymentId) ?? throw new BusinessException("红包记录不存在");
            if (payment.Status != 2)
                throw new BusinessException("只能重新发放失败的红包");

            // 重置状态为待发放
            var result = await _wechatPaymentDAL.UpdatePaymentStatusAsync(paymentId, 0, null, null);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "微信红包",
                Operation = "重新发放红包",
                BusinessObject = "WechatPayment",
                ObjectId = paymentId.ToString(),
                DetailedInfo = $"重新发放失败的红包，订单号：{payment.OutTradeNo}",
                BeforeData = new { Status = 2 },
                AfterData = new { Status = 0 },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 生成商户订单号
        /// </summary>
        /// <returns>商户订单号</returns>
        private static string GenerateOutTradeNo()
        {
            return $"REWARD{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
        }

        /// <summary>
        /// 生成订单号
        /// </summary>
        /// <returns>订单号</returns>
        private static string GenerateOrderNo()
        {
            return $"WXP{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
        }

        /// <summary>
        /// 生成退款单号
        /// </summary>
        /// <returns>退款单号</returns>
        private static string GenerateRefundNo()
        {
            return $"WXR{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
        }

        /// <summary>
        /// 处理红包发放成功后的业务逻辑
        /// </summary>
        /// <param name="payment">支付记录</param>
        private async Task ProcessSuccessfulPaymentAsync(WechatPayment payment)
        {
            // 红包发放成功后的业务逻辑
            // 1. 更新红包记录表中的状态
            // 2. 记录用户红包领取记录
            // 3. 发送通知给用户
            // 此处仅为示例，实际业务逻辑根据需求实现
            await Task.CompletedTask;
        }

        /// <summary>
        /// 转换实体为响应DTO
        /// </summary>
        /// <param name="wechatPayment">微信支付实体</param>
        /// <returns>响应DTO</returns>
        private static WechatPaymentResponseDto ConvertToResponseDto(WechatPayment wechatPayment)
        {
            return new WechatPaymentResponseDto
            {
                Id = wechatPayment.Id,
                RewardId = wechatPayment.RewardId,
                UserId = wechatPayment.UserId,
                OpenId = wechatPayment.OpenId,
                OutTradeNo = wechatPayment.OutTradeNo,
                OrderNo = wechatPayment.OrderNo,
                Amount = wechatPayment.Amount, // Amount字段是int类型（分）
                Currency = wechatPayment.Currency,
                Subject = wechatPayment.Subject,
                Body = wechatPayment.Body,
                PaymentType = wechatPayment.PaymentType,
                Status = wechatPayment.Status,
                TransactionId = wechatPayment.TransactionId,
                PayTime = wechatPayment.PayTime,
                RefundNo = wechatPayment.RefundNo,
                RefundAmount = wechatPayment.RefundAmount.HasValue ? wechatPayment.RefundAmount.Value / 100m : 0m,
                RefundReason = wechatPayment.RefundReason,
                RefundTime = wechatPayment.RefundTime,
                Description = wechatPayment.Description,
                CreateTime = wechatPayment.CreateTime
            };
        }
    }

    /// <summary>
    /// 红包发放请求模型
    /// </summary>
    public class RewardRequest
    {
        /// <summary>
        /// 红包记录ID
        /// </summary>
        public int RewardId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户微信OpenID
        /// </summary>
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 红包金额（元）
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 红包描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
